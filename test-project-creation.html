<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Project Creation Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
      }
      .test-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      .success {
        background-color: #d4edda;
        border-color: #c3e6cb;
      }
      .error {
        background-color: #f8d7da;
        border-color: #f5c6cb;
      }
      .info {
        background-color: #d1ecf1;
        border-color: #bee5eb;
      }
      button {
        padding: 10px 20px;
        margin: 5px;
        cursor: pointer;
      }
      .log {
        background: #f8f9fa;
        padding: 10px;
        margin: 10px 0;
        border-radius: 3px;
        font-family: monospace;
      }
    </style>
  </head>
  <body>
    <h1>🧪 Project Creation Workflow Test</h1>

    <div class="test-section info">
      <h3>Test Instructions</h3>
      <p>This test will verify each step of the project creation workflow:</p>
      <ol>
        <li>Check if frontend is accessible</li>
        <li>Test authentication</li>
        <li>Test API connectivity</li>
        <li>Test project creation endpoint</li>
      </ol>
    </div>

    <div class="test-section">
      <h3>Step 1: Frontend Accessibility</h3>
      <button onclick="testFrontend()">Test Frontend</button>
      <div id="frontend-result" class="log"></div>
    </div>

    <div class="test-section">
      <h3>Step 2: Authentication Test</h3>
      <button onclick="testAuth()">Test Login</button>
      <div id="auth-result" class="log"></div>
    </div>

    <div class="test-section">
      <h3>Step 3: API Connectivity</h3>
      <button onclick="testAPI()">Test API</button>
      <div id="api-result" class="log"></div>
    </div>

    <div class="test-section">
      <h3>Step 4: Project Creation</h3>
      <button onclick="testProjectCreation()">Test Project Creation</button>
      <div id="project-result" class="log"></div>
    </div>

    <div class="test-section">
      <h3>Step 5: Manual Modal Test</h3>
      <p>
        Open
        <a
          href="http://localhost:3001/founder-dashboard/projects"
          target="_blank"
          >Projects Page</a
        >
        and test the modal manually.
      </p>
      <button onclick="openProjectsPage()">Open Projects Page</button>
    </div>

    <script>
      const API_BASE = "http://localhost:8000/api";
      const FRONTEND_BASE = "http://localhost:3001";
      let authToken = null;

      function log(elementId, message, type = "info") {
        const element = document.getElementById(elementId);
        const timestamp = new Date().toLocaleTimeString();
        element.innerHTML += `[${timestamp}] ${message}\n`;

        // Update section styling
        const section = element.closest(".test-section");
        section.className = section.className.replace(
          /success|error|info/,
          type
        );
      }

      async function testFrontend() {
        log("frontend-result", "🔍 Testing frontend accessibility...", "info");
        try {
          const response = await fetch(
            `${FRONTEND_BASE}/founder-dashboard/projects`
          );
          if (response.ok) {
            log("frontend-result", "✅ Frontend is accessible", "success");
          } else {
            log(
              "frontend-result",
              `❌ Frontend returned status: ${response.status}`,
              "error"
            );
          }
        } catch (error) {
          log(
            "frontend-result",
            `❌ Frontend connection failed: ${error.message}`,
            "error"
          );
        }
      }

      async function testAuth() {
        log("auth-result", "🔐 Testing authentication...", "info");
        try {
          const response = await fetch(`${API_BASE}/auth/login/`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              username: "founder", // Backend expects username, not email
              password: "demo123",
            }),
          });

          if (response.ok) {
            const data = await response.json();
            authToken = data.access;
            log("auth-result", "✅ Authentication successful", "success");
            log(
              "auth-result",
              `Token: ${authToken.substring(0, 20)}...`,
              "success"
            );
            log(
              "auth-result",
              `User: ${data.user.email} (${data.user.role})`,
              "success"
            );
          } else {
            const errorData = await response.text();
            log(
              "auth-result",
              `❌ Authentication failed: ${response.status}`,
              "error"
            );
            log("auth-result", `Error: ${errorData}`, "error");
          }
        } catch (error) {
          log(
            "auth-result",
            `❌ Authentication error: ${error.message}`,
            "error"
          );
        }
      }

      async function testAPI() {
        log("api-result", "🔌 Testing API connectivity...", "info");

        if (!authToken) {
          log(
            "api-result",
            "❌ No auth token available. Run authentication test first.",
            "error"
          );
          return;
        }

        try {
          // Test projects endpoint
          const response = await fetch(`${API_BASE}/projects/`, {
            headers: {
              Authorization: `Bearer ${authToken}`,
              "Content-Type": "application/json",
            },
          });

          if (response.ok) {
            const data = await response.json();
            log("api-result", "✅ Projects API accessible", "success");
            log(
              "api-result",
              `Found ${data.results ? data.results.length : data.length} projects`,
              "success"
            );
          } else {
            const errorData = await response.text();
            log("api-result", `❌ API failed: ${response.status}`, "error");
            log("api-result", `Error: ${errorData}`, "error");
          }
        } catch (error) {
          log(
            "api-result",
            `❌ API connection error: ${error.message}`,
            "error"
          );
        }
      }

      async function testProjectCreation() {
        log("project-result", "🚀 Testing project creation...", "info");

        if (!authToken) {
          log(
            "project-result",
            "❌ No auth token available. Run authentication test first.",
            "error"
          );
          return;
        }

        const testProject = {
          name: "Test Project من API",
          description: "مشروع تجريبي لاختبار إنشاء المشاريع",
          type: "website",
          status: "planning",
          priority: "medium",
          client: 1,
          start_date: "2024-01-15",
          deadline: "2024-03-15",
          budget: 50000,
          domains: ["test.example.com"],
          repository_url: "https://github.com/test/project",
          staging_url: "https://staging.test.com",
          production_url: "https://test.com",
        };

        try {
          const response = await fetch(`${API_BASE}/projects/`, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${authToken}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify(testProject),
          });

          if (response.ok) {
            const data = await response.json();
            log(
              "project-result",
              "✅ Project created successfully!",
              "success"
            );
            log("project-result", `Project ID: ${data.id}`, "success");
            log("project-result", `Project Name: ${data.name}`, "success");
          } else {
            const errorData = await response.text();
            log(
              "project-result",
              `❌ Project creation failed: ${response.status}`,
              "error"
            );
            log("project-result", `Error: ${errorData}`, "error");
          }
        } catch (error) {
          log(
            "project-result",
            `❌ Project creation error: ${error.message}`,
            "error"
          );
        }
      }

      function openProjectsPage() {
        window.open(`${FRONTEND_BASE}/founder-dashboard/projects`, "_blank");
      }

      // Auto-run tests on page load
      window.onload = function () {
        log("frontend-result", "Ready to test frontend...", "info");
        log("auth-result", "Ready to test authentication...", "info");
        log("api-result", "Ready to test API...", "info");
        log("project-result", "Ready to test project creation...", "info");
      };
    </script>
  </body>
</html>
