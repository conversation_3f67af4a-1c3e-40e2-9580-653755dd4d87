from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Avg, Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import TeamMember, TeamPerformanceMetric
from .serializers import (
    TeamMemberSerializer,
    TeamMemberListSerializer,
    TeamMemberCreateSerializer,
    TeamMemberDetailSerializer,
    TeamMemberUpdateSerializer,
    TeamPerformanceMetricSerializer,
    TeamPerformanceMetricCreateSerializer,
    TeamStatsSerializer
)


class TeamMemberViewSet(viewsets.ModelViewSet):
    """Team member management viewset"""
    queryset = TeamMember.objects.all().select_related('user').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'status', 'position']
    search_fields = ['user__first_name', 'user__last_name', 'user__username', 'employee_id', 'position']
    ordering_fields = ['user__first_name', 'created_at', 'hire_date', 'performance_score', 'completed_projects']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return TeamMemberListSerializer
        elif self.action == 'create':
            return TeamMemberCreateSerializer
        elif self.action == 'retrieve':
            return TeamMemberDetailSerializer
        elif self.action in ['update', 'partial_update']:
            return TeamMemberUpdateSerializer
        return TeamMemberSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admins and sales managers can see all team members
        if user.role in ['admin', 'sales_manager']:
            return queryset

        # Other users can see all team members but with limited information
        return queryset

    def perform_create(self, serializer):
        """Create team member"""
        serializer.save()

    def perform_update(self, serializer):
        """Update team member"""
        serializer.save()

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get team statistics"""
        queryset = self.get_queryset()

        # Basic counts
        total_members = queryset.count()
        active_members = queryset.filter(status='active').count()
        inactive_members = queryset.filter(status='inactive').count()
        on_leave_members = queryset.filter(status='on_leave').count()

        # Department distribution
        department_counts = queryset.values('department').annotate(count=Count('id'))
        department_distribution = {item['department']: item['count'] for item in department_counts}

        # Performance metrics
        avg_performance = queryset.aggregate(avg=Avg('performance_score'))['avg'] or 0
        total_completed_projects = queryset.aggregate(total=Sum('completed_projects'))['total'] or 0
        total_completed_tasks = queryset.aggregate(total=Sum('total_tasks_completed'))['total'] or 0

        # Top performers (top 5 by productivity score)
        top_performers = list(
            queryset.annotate(
                productivity=Count('completed_projects') * 10 + Count('total_tasks_completed') * 2
            ).order_by('-productivity')[:5].values(
                'id', 'user__first_name', 'user__last_name', 'department', 'productivity'
            )
        )

        stats_data = {
            'total_members': total_members,
            'active_members': active_members,
            'inactive_members': inactive_members,
            'on_leave_members': on_leave_members,
            'department_distribution': department_distribution,
            'average_performance_score': round(avg_performance, 1),
            'total_completed_projects': total_completed_projects,
            'total_completed_tasks': total_completed_tasks,
            'top_performers': top_performers
        }

        serializer = TeamStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_performance(self, request, pk=None):
        """Update team member performance metrics"""
        team_member = self.get_object()

        # Update basic performance fields
        performance_score = request.data.get('performance_score')
        completed_projects = request.data.get('completed_projects')
        total_tasks_completed = request.data.get('total_tasks_completed')

        if performance_score is not None:
            team_member.performance_score = performance_score
        if completed_projects is not None:
            team_member.completed_projects = completed_projects
        if total_tasks_completed is not None:
            team_member.total_tasks_completed = total_tasks_completed

        team_member.save()

        serializer = self.get_serializer(team_member)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def performance_history(self, request, pk=None):
        """Get performance history for team member"""
        team_member = self.get_object()
        metrics = team_member.performance_metrics.all().order_by('-period_start')

        serializer = TeamPerformanceMetricSerializer(metrics, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def departments(self, request):
        """Get list of departments with member counts"""
        departments = TeamMember.objects.values('department').annotate(
            count=Count('id'),
            active_count=Count('id', filter=Q(status='active'))
        ).order_by('department')

        return Response(departments)

    @action(detail=False, methods=['get'])
    def recent_hires(self, request):
        """Get recently hired team members (last 30 days)"""
        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        recent_hires = self.get_queryset().filter(hire_date__gte=thirty_days_ago)

        serializer = TeamMemberListSerializer(recent_hires, many=True)
        return Response(serializer.data)
