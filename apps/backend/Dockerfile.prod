# Multi-stage build for production
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        libpq-dev \
        curl \
    && rm -rf /var/lib/apt/lists/*

# Create and set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=mtbrmg_erp.settings.production

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        libpq5 \
        curl \
        gettext \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r mtbrmg && useradd -r -g mtbrmg mtbrmg

# Create directories
RUN mkdir -p /app/static /app/media /app/logs \
    && chown -R mtbrmg:mtbrmg /app

# Set work directory
WORKDIR /app

# Copy Python dependencies from builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy project files
COPY --chown=mtbrmg:mtbrmg . .

# Create entrypoint script
RUN cat > /app/entrypoint.prod.sh << 'EOF'
#!/bin/bash
set -e

echo "Starting MTBRMG ERP Production Server..."

# Wait for database
echo "Waiting for database..."
while ! nc -z postgres 5432; do
  sleep 0.1
done
echo "Database is ready!"

# Wait for Redis
echo "Waiting for Redis..."
while ! nc -z redis 6379; do
  sleep 0.1
done
echo "Redis is ready!"

# Run migrations
echo "Running database migrations..."
python manage.py migrate --noinput

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput --clear

# Create superuser if it doesn't exist
echo "Creating superuser if needed..."
python manage.py shell -c "
from authentication.models import User
if not User.objects.filter(username='founder').exists():
    User.objects.create_superuser(
        username='founder',
        email='<EMAIL>',
        password='demo123',
        first_name='محمد',
        last_name='يوسف',
        role='admin'
    )
    print('Superuser created successfully')
else:
    print('Superuser already exists')
"

# Compile messages
echo "Compiling translation messages..."
python manage.py compilemessages

# Start Gunicorn
echo "Starting Gunicorn server..."
exec gunicorn mtbrmg_erp.wsgi:application \
    --bind 0.0.0.0:8000 \
    --workers 4 \
    --worker-class gevent \
    --worker-connections 1000 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --timeout 30 \
    --keep-alive 2 \
    --log-level info \
    --access-logfile /app/logs/gunicorn_access.log \
    --error-logfile /app/logs/gunicorn_error.log \
    --capture-output
EOF

# Make entrypoint executable
RUN chmod +x /app/entrypoint.prod.sh

# Install additional production dependencies
RUN pip install --no-cache-dir gunicorn[gevent] dj-database-url

# Change ownership
RUN chown -R mtbrmg:mtbrmg /app

# Switch to non-root user
USER mtbrmg

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/admin/ || exit 1

# Set entrypoint
ENTRYPOINT ["/app/entrypoint.prod.sh"]
