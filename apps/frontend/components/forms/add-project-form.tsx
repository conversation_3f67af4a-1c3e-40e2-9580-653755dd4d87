'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  FolderOpen,
  Calendar,
  DollarSign,
  Users,
  Globe,
  FileText,
  Save,
  X,
  Loader2
} from 'lucide-react';

interface AddProjectFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (projectData: ProjectFormData) => Promise<void>;
  clients?: Array<{ id: string | number; name: string; email?: string; company?: string }>;
}

export interface ProjectFormData {
  name: string;
  description: string;
  type: 'website' | 'mobile_app' | 'web_app' | 'ecommerce' | 'branding' | 'marketing' | 'other';
  status: 'planning' | 'development' | 'testing' | 'completed' | 'on_hold';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  client_id: string;
  start_date: string;
  end_date: string;
  deadline: string;
  budget: number;
  domains: string;
  repository_url: string;
  staging_url: string;
  production_url: string;
}

const PROJECT_TYPES = [
  { value: 'website', label: 'موقع إلكتروني' },
  { value: 'mobile_app', label: 'تطبيق جوال' },
  { value: 'web_app', label: 'تطبيق ويب' },
  { value: 'ecommerce', label: 'متجر إلكتروني' },
  { value: 'branding', label: 'هوية تجارية' },
  { value: 'marketing', label: 'تسويق رقمي' },
  { value: 'other', label: 'أخرى' }
];

const PROJECT_STATUS = [
  { value: 'planning', label: 'التخطيط' },
  { value: 'development', label: 'التطوير' },
  { value: 'testing', label: 'الاختبار' },
  { value: 'completed', label: 'مكتمل' },
  { value: 'on_hold', label: 'متوقف' }
];

const PROJECT_PRIORITY = [
  { value: 'low', label: 'منخفضة' },
  { value: 'medium', label: 'متوسطة' },
  { value: 'high', label: 'عالية' },
  { value: 'urgent', label: 'عاجلة' }
];

export function AddProjectForm({ isOpen, onClose, onSubmit, clients = [] }: AddProjectFormProps) {

  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    description: '',
    type: 'website',
    status: 'planning',
    priority: 'medium',
    client_id: clients.length > 0 ? String(clients[0].id) : '1',
    start_date: '',
    end_date: '',
    deadline: '',
    budget: 0,
    domains: '',
    repository_url: '',
    staging_url: '',
    production_url: ''
  });

  const [errors, setErrors] = useState<Partial<Record<keyof ProjectFormData, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof ProjectFormData, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المشروع مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف المشروع مطلوب';
    }

    if (!formData.client_id) {
      newErrors.client_id = 'العميل مطلوب';
    }

    if (!formData.start_date) {
      newErrors.start_date = 'تاريخ البداية مطلوب';
    }

    if (!formData.deadline) {
      newErrors.deadline = 'الموعد النهائي مطلوب';
    }

    if (formData.budget <= 0) {
      newErrors.budget = 'الميزانية يجب أن تكون أكبر من صفر';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      handleClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      type: 'website',
      status: 'planning',
      priority: 'medium',
      client_id: '1',
      start_date: '',
      end_date: '',
      deadline: '',
      budget: 0,
      domains: '',
      repository_url: '',
      staging_url: '',
      production_url: ''
    });
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const handleInputChange = (field: keyof ProjectFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FolderOpen className="h-5 w-5 text-purple-600" />
              إضافة مشروع جديد
            </DialogTitle>
            <DialogDescription>
              املأ المعلومات أدناه لإضافة مشروع جديد إلى النظام
            </DialogDescription>
          </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">المعلومات الأساسية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">اسم المشروع *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="أدخل اسم المشروع"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name}</p>}
                </div>

                <div>
                  <Label htmlFor="type">نوع المشروع *</Label>
                  <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع المشروع" />
                    </SelectTrigger>
                    <SelectContent>
                      {PROJECT_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">وصف المشروع *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="أدخل وصف تفصيلي للمشروع"
                  rows={3}
                  className={errors.description ? 'border-red-500' : ''}
                />
                {errors.description && <p className="text-sm text-red-500 mt-1">{errors.description}</p>}
              </div>

              <div>
                <Label htmlFor="client_id">العميل *</Label>
                <Select value={formData.client_id} onValueChange={(value) => handleInputChange('client_id', value)}>
                  <SelectTrigger className={errors.client_id ? 'border-red-500' : ''}>
                    <SelectValue placeholder="اختر العميل" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.length > 0 ? (
                      clients.map((client) => (
                        <SelectItem key={client.id} value={String(client.id)}>
                          {client.name} {client.company && `(${client.company})`}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="1" disabled>
                        لا توجد عملاء متاحين
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {errors.client_id && <p className="text-sm text-red-500 mt-1">{errors.client_id}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="status">حالة المشروع</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر حالة المشروع" />
                    </SelectTrigger>
                    <SelectContent>
                      {PROJECT_STATUS.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="priority">الأولوية</Label>
                  <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الأولوية" />
                    </SelectTrigger>
                    <SelectContent>
                      {PROJECT_PRIORITY.map((priority) => (
                        <SelectItem key={priority.value} value={priority.value}>
                          {priority.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timeline & Budget */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">الجدول الزمني والميزانية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="start_date">تاريخ البداية *</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => handleInputChange('start_date', e.target.value)}
                    className={errors.start_date ? 'border-red-500' : ''}
                  />
                  {errors.start_date && <p className="text-sm text-red-500 mt-1">{errors.start_date}</p>}
                </div>

                <div>
                  <Label htmlFor="end_date">تاريخ النهاية المتوقع</Label>
                  <Input
                    id="end_date"
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => handleInputChange('end_date', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="deadline">الموعد النهائي *</Label>
                  <Input
                    id="deadline"
                    type="date"
                    value={formData.deadline}
                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                    className={errors.deadline ? 'border-red-500' : ''}
                  />
                  {errors.deadline && <p className="text-sm text-red-500 mt-1">{errors.deadline}</p>}
                </div>
              </div>

              <div>
                <Label htmlFor="budget">الميزانية (جنيه مصري) *</Label>
                <Input
                  id="budget"
                  type="number"
                  min="0"
                  step="100"
                  value={formData.budget}
                  onChange={(e) => handleInputChange('budget', parseFloat(e.target.value) || 0)}
                  placeholder="أدخل ميزانية المشروع"
                  className={errors.budget ? 'border-red-500' : ''}
                />
                {errors.budget && <p className="text-sm text-red-500 mt-1">{errors.budget}</p>}
              </div>
            </CardContent>
          </Card>

          {/* Technical Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">التفاصيل التقنية</CardTitle>
              <CardDescription>معلومات اختيارية للمشاريع التقنية</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="domains">النطاقات (مفصولة بفواصل)</Label>
                <Input
                  id="domains"
                  value={formData.domains}
                  onChange={(e) => handleInputChange('domains', e.target.value)}
                  placeholder="example.com, www.example.com"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="repository_url">رابط المستودع</Label>
                  <Input
                    id="repository_url"
                    value={formData.repository_url}
                    onChange={(e) => handleInputChange('repository_url', e.target.value)}
                    placeholder="https://github.com/username/repo"
                  />
                </div>

                <div>
                  <Label htmlFor="staging_url">رابط بيئة الاختبار</Label>
                  <Input
                    id="staging_url"
                    value={formData.staging_url}
                    onChange={(e) => handleInputChange('staging_url', e.target.value)}
                    placeholder="https://staging.example.com"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="production_url">رابط الموقع النهائي</Label>
                <Input
                  id="production_url"
                  value={formData.production_url}
                  onChange={(e) => handleInputChange('production_url', e.target.value)}
                  placeholder="https://example.com"
                />
              </div>
            </CardContent>
          </Card>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ المشروع'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
