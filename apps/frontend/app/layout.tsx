import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { IBM_Plex_Sans_Arabic } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { QueryProvider } from "@/components/query-provider"
import { AuthProvider } from "@/components/auth-provider"
import { Toaster } from "@/components/ui/sonner"

const ibmPlexSansArabic = IBM_Plex_Sans_Arabic({
  subsets: ["arabic"],
  weight: ["100", "200", "300", "400", "500", "600", "700"],
  variable: "--font-ibm-plex-arabic",
  display: "swap",
})

export const metadata: Metadata = {
  title: "MTBRMG ERP - نظام إدارة الوكالة الرقمية",
  description: "نظام إدارة شامل للوكالة الرقمية المصرية مع دعم كامل للغة العربية",
  keywords: "ERP, إدارة المشاريع, العملاء, المهام, الفريق",
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/the_logo.png',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className={ibmPlexSansArabic.variable}>
      <body className="font-arabic antialiased">
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
          storageKey="mtbrmg-theme"
        >
          <QueryProvider>
            <AuthProvider>
              {children}
              <Toaster position="top-center" />
            </AuthProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
