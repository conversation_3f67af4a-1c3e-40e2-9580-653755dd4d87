#!/bin/bash

# MTBRMG ERP System Monitoring Script
# Usage: ./scripts/monitor.sh [action]
# Actions: status, logs, restart, backup, health

set -e

# Configuration
COMPOSE_FILE="docker-compose.prod.yml"
LOG_DIR="./logs"
BACKUP_DIR="./backups"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Helper functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Check system status
check_status() {
    log "Checking MTBRMG ERP system status..."
    
    echo "=== Docker Containers ==="
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo -e "\n=== Service Health ==="
    
    # Check backend
    if curl -f http://localhost:8001/admin/ &> /dev/null; then
        echo -e "Backend API: ${GREEN}✓ Healthy${NC}"
    else
        echo -e "Backend API: ${RED}✗ Unhealthy${NC}"
    fi
    
    # Check frontend
    if curl -f http://localhost:3000/founder-dashboard &> /dev/null; then
        echo -e "Frontend: ${GREEN}✓ Healthy${NC}"
    else
        echo -e "Frontend: ${RED}✗ Unhealthy${NC}"
    fi
    
    # Check database
    if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U mtbrmg_user &> /dev/null; then
        echo -e "Database: ${GREEN}✓ Healthy${NC}"
    else
        echo -e "Database: ${RED}✗ Unhealthy${NC}"
    fi
    
    # Check Redis
    if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping &> /dev/null; then
        echo -e "Redis: ${GREEN}✓ Healthy${NC}"
    else
        echo -e "Redis: ${RED}✗ Unhealthy${NC}"
    fi
    
    echo -e "\n=== System Resources ==="
    echo "CPU Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
    
    echo -e "\n=== Disk Usage ==="
    df -h | grep -E "(Filesystem|/dev/)"
    
    echo -e "\n=== Docker Volumes ==="
    docker volume ls | grep mtbrmg
}

# Show logs
show_logs() {
    local service=${1:-""}
    
    if [ -z "$service" ]; then
        log "Showing logs for all services..."
        docker-compose -f "$COMPOSE_FILE" logs --tail=50 -f
    else
        log "Showing logs for service: $service"
        docker-compose -f "$COMPOSE_FILE" logs --tail=50 -f "$service"
    fi
}

# Restart services
restart_services() {
    local service=${1:-""}
    
    if [ -z "$service" ]; then
        log "Restarting all services..."
        docker-compose -f "$COMPOSE_FILE" restart
    else
        log "Restarting service: $service"
        docker-compose -f "$COMPOSE_FILE" restart "$service"
    fi
    
    # Wait and check health
    sleep 10
    check_health
}

# Perform backup
perform_backup() {
    log "Starting backup process..."
    
    BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
    mkdir -p "$BACKUP_PATH"
    
    # Backup database
    info "Backing up database..."
    docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U mtbrmg_user mtbrmg_erp_prod > "$BACKUP_PATH/database.sql"
    
    # Backup media files
    info "Backing up media files..."
    docker cp mtbrmg_backend_prod:/app/media "$BACKUP_PATH/"
    
    # Backup logs
    info "Backing up logs..."
    cp -r "$LOG_DIR" "$BACKUP_PATH/"
    
    # Compress backup
    info "Compressing backup..."
    tar -czf "$BACKUP_PATH.tar.gz" -C "$BACKUP_DIR" "backup_$BACKUP_TIMESTAMP"
    rm -rf "$BACKUP_PATH"
    
    log "Backup completed: $BACKUP_PATH.tar.gz"
    
    # Cleanup old backups (keep last 7 days)
    find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +7 -delete
}

# Health check
check_health() {
    log "Performing comprehensive health check..."
    
    # Check all endpoints
    local endpoints=(
        "http://localhost:8001/admin/"
        "http://localhost:3000/founder-dashboard"
        "http://localhost:8001/api/auth/login/"
        "http://localhost:8001/api/clients/"
        "http://localhost:8001/api/projects/"
        "http://localhost:8001/api/tasks/"
        "http://localhost:8001/api/team/"
    )
    
    local failed=0
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f "$endpoint" &> /dev/null; then
            echo -e "$endpoint: ${GREEN}✓${NC}"
        else
            echo -e "$endpoint: ${RED}✗${NC}"
            ((failed++))
        fi
    done
    
    # Check database connectivity
    if docker-compose -f "$COMPOSE_FILE" exec -T backend python manage.py check --database default &> /dev/null; then
        echo -e "Database connectivity: ${GREEN}✓${NC}"
    else
        echo -e "Database connectivity: ${RED}✗${NC}"
        ((failed++))
    fi
    
    # Check Redis connectivity
    if docker-compose -f "$COMPOSE_FILE" exec -T backend python manage.py shell -c "from django.core.cache import cache; cache.set('test', 'ok'); print('Redis OK')" &> /dev/null; then
        echo -e "Redis connectivity: ${GREEN}✓${NC}"
    else
        echo -e "Redis connectivity: ${RED}✗${NC}"
        ((failed++))
    fi
    
    if [ $failed -eq 0 ]; then
        log "All health checks passed!"
        return 0
    else
        error "$failed health checks failed!"
        return 1
    fi
}

# Show system metrics
show_metrics() {
    log "System metrics and statistics..."
    
    echo "=== Container Statistics ==="
    docker stats --no-stream
    
    echo -e "\n=== Database Statistics ==="
    docker-compose -f "$COMPOSE_FILE" exec -T backend python manage.py shell -c "
from authentication.models import User
from clients.models import Client
from projects.models import Project
from tasks.models import Task
from team.models import TeamMember

print(f'Users: {User.objects.count()}')
print(f'Clients: {Client.objects.count()}')
print(f'Projects: {Project.objects.count()}')
print(f'Tasks: {Task.objects.count()}')
print(f'Team Members: {TeamMember.objects.count()}')
"
    
    echo -e "\n=== Recent Activity ==="
    docker-compose -f "$COMPOSE_FILE" logs --tail=10 backend | grep -E "(INFO|ERROR|WARNING)"
}

# Main function
main() {
    local action=${1:-"status"}
    
    case $action in
        "status")
            check_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "restart")
            restart_services "$2"
            ;;
        "backup")
            perform_backup
            ;;
        "health")
            check_health
            ;;
        "metrics")
            show_metrics
            ;;
        *)
            echo "Usage: $0 [action] [service]"
            echo "Actions:"
            echo "  status  - Show system status"
            echo "  logs    - Show logs (optionally for specific service)"
            echo "  restart - Restart services (optionally specific service)"
            echo "  backup  - Perform system backup"
            echo "  health  - Run health checks"
            echo "  metrics - Show system metrics"
            echo ""
            echo "Services: backend, frontend, postgres, redis, celery, celery-beat, nginx"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
