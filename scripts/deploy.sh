#!/bin/bash

# MTBRMG ERP Production Deployment Script
# Usage: ./scripts/deploy.sh [environment]

set -e

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_NAME="mtbrmg-erp"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p logs backups nginx/ssl
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if environment file exists
    if [ ! -f ".env.prod" ]; then
        warning "Production environment file (.env.prod) not found."
        info "Please copy .env.production to .env.prod and update the values."
        exit 1
    fi
    
    log "Prerequisites check completed successfully."
}

# Backup current deployment
backup_current() {
    log "Creating backup of current deployment..."
    
    if [ -f "docker-compose.prod.yml" ]; then
        # Create backup directory with timestamp
        BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
        mkdir -p "$BACKUP_PATH"
        
        # Backup database
        info "Backing up database..."
        docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U mtbrmg_user mtbrmg_erp_prod > "$BACKUP_PATH/database.sql" || warning "Database backup failed"
        
        # Backup media files
        info "Backing up media files..."
        docker cp mtbrmg_backend_prod:/app/media "$BACKUP_PATH/" || warning "Media backup failed"
        
        # Backup configuration
        cp .env.prod "$BACKUP_PATH/" || warning "Environment backup failed"
        
        log "Backup completed: $BACKUP_PATH"
    else
        info "No existing deployment found. Skipping backup."
    fi
}

# Build and deploy
deploy() {
    log "Starting deployment process..."
    
    # Pull latest images
    info "Pulling latest base images..."
    docker-compose -f docker-compose.prod.yml pull postgres redis nginx
    
    # Build application images
    info "Building application images..."
    docker-compose -f docker-compose.prod.yml build --no-cache
    
    # Stop existing containers
    info "Stopping existing containers..."
    docker-compose -f docker-compose.prod.yml down || true
    
    # Start new deployment
    info "Starting new deployment..."
    docker-compose -f docker-compose.prod.yml up -d
    
    # Wait for services to be ready
    info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_health
    
    log "Deployment completed successfully!"
}

# Check service health
check_health() {
    log "Checking service health..."
    
    # Check backend health
    info "Checking backend health..."
    for i in {1..30}; do
        if curl -f http://localhost:8001/admin/ &> /dev/null; then
            log "Backend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            error "Backend health check failed"
        fi
        sleep 2
    done
    
    # Check frontend health
    info "Checking frontend health..."
    for i in {1..30}; do
        if curl -f http://localhost:3000/founder-dashboard &> /dev/null; then
            log "Frontend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            error "Frontend health check failed"
        fi
        sleep 2
    done
    
    # Check database connectivity
    info "Checking database connectivity..."
    docker-compose -f docker-compose.prod.yml exec -T backend python manage.py check --database default || error "Database check failed"
    
    log "All health checks passed!"
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old images and containers..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes (be careful with this)
    # docker volume prune -f
    
    log "Cleanup completed."
}

# Generate SSL certificates (self-signed for development)
generate_ssl() {
    log "Generating SSL certificates..."
    
    if [ ! -f "nginx/ssl/cert.pem" ]; then
        info "Creating self-signed SSL certificate..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=EG/ST=Cairo/L=Cairo/O=MTBRMG/OU=IT/CN=localhost"
        
        log "SSL certificates generated. For production, replace with valid certificates."
    else
        info "SSL certificates already exist."
    fi
}

# Main deployment process
main() {
    log "Starting MTBRMG ERP deployment for environment: $ENVIRONMENT"
    
    create_directories
    check_prerequisites
    generate_ssl
    backup_current
    deploy
    cleanup
    
    log "Deployment completed successfully!"
    info "Application is available at:"
    info "  - Frontend: http://localhost:3000"
    info "  - Backend API: http://localhost:8001"
    info "  - Admin Panel: http://localhost:8001/admin/"
    info ""
    info "Default credentials:"
    info "  - Username: founder"
    info "  - Password: demo123"
    info ""
    warning "Please change default credentials in production!"
}

# Run main function
main "$@"
