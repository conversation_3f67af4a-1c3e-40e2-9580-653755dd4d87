version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: mtbrmg_postgres_prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mtbrmg_erp_prod}
      POSTGRES_USER: ${POSTGRES_USER:-mtbrmg_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_2024}
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-mtbrmg_user} -d ${POSTGRES_DB:-mtbrmg_erp_prod}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7-alpine
    container_name: mtbrmg_redis_prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_secure_2024}
    volumes:
      - redis_data_prod:/data
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  backend:
    build:
      context: ./apps/backend
      dockerfile: Dockerfile.prod
    container_name: mtbrmg_backend_prod
    environment:
      - DEBUG=False
      - DJANGO_SETTINGS_MODULE=mtbrmg_erp.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-mtbrmg_user}:${POSTGRES_PASSWORD:-secure_password_2024}@postgres:5432/${POSTGRES_DB:-mtbrmg_erp_prod}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_secure_2024}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY:-your-super-secret-key-change-in-production}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1,your-domain.com}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-https://your-domain.com,https://www.your-domain.com}
      - EMAIL_HOST=${EMAIL_HOST:-smtp.gmail.com}
      - EMAIL_PORT=${EMAIL_PORT:-587}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER:-<EMAIL>}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD:-your-app-password}
    volumes:
      - static_files_prod:/app/static
      - media_files_prod:/app/media
      - ./logs:/app/logs
    ports:
      - "8001:8000"  # Different port to avoid conflicts
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/admin/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  celery:
    build:
      context: ./apps/backend
      dockerfile: Dockerfile.prod
    container_name: mtbrmg_celery_prod
    command: celery -A mtbrmg_erp worker -l info
    environment:
      - DEBUG=False
      - DJANGO_SETTINGS_MODULE=mtbrmg_erp.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-mtbrmg_user}:${POSTGRES_PASSWORD:-secure_password_2024}@postgres:5432/${POSTGRES_DB:-mtbrmg_erp_prod}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_secure_2024}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY:-your-super-secret-key-change-in-production}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
      - backend
    restart: unless-stopped

  celery-beat:
    build:
      context: ./apps/backend
      dockerfile: Dockerfile.prod
    container_name: mtbrmg_celery_beat_prod
    command: celery -A mtbrmg_erp beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DEBUG=False
      - DJANGO_SETTINGS_MODULE=mtbrmg_erp.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-mtbrmg_user}:${POSTGRES_PASSWORD:-secure_password_2024}@postgres:5432/${POSTGRES_DB:-mtbrmg_erp_prod}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_secure_2024}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY:-your-super-secret-key-change-in-production}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
      - backend
    restart: unless-stopped

  frontend:
    build:
      context: ./apps/frontend
      dockerfile: Dockerfile.prod
    container_name: mtbrmg_frontend_prod
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-https://api.your-domain.com}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-https://your-domain.com}
    ports:
      - "3000:3000"  # Standard production port
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/founder-dashboard"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: mtbrmg_nginx_prod
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - static_files_prod:/var/www/static
      - media_files_prod:/var/www/media
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  static_files_prod:
    driver: local
  media_files_prod:
    driver: local

networks:
  default:
    name: mtbrmg_network_prod
