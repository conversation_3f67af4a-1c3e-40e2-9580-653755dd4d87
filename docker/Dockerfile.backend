# Use Python 3.11 slim image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    postgresql-client \
    build-essential \
    libpq-dev \
    gettext \
    curl \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY apps/backend/requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY apps/backend /app

# Create necessary directories
RUN mkdir -p /app/logs /app/staticfiles /app/media

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/health/ || exit 1

# Run the application with inline startup commands
CMD ["sh", "-c", "\
    echo 'Waiting for PostgreSQL...' && \
    while ! pg_isready -h postgres -p 5432 -U postgres; do sleep 2; done && \
    echo 'Waiting for Redis...' && \
    while ! redis-cli -h redis ping; do sleep 2; done && \
    echo 'Running migrations...' && \
    python manage.py makemigrations && \
    python manage.py migrate && \
    echo 'Creating superuser...' && \
    python manage.py shell -c \"from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.filter(username='admin').exists() or User.objects.create_superuser('admin', '<EMAIL>', 'admin123')\" && \
    echo 'Collecting static files...' && \
    python manage.py collectstatic --noinput --clear && \
    echo 'Starting server...' && \
    python manage.py runserver 0.0.0.0:8000"]
